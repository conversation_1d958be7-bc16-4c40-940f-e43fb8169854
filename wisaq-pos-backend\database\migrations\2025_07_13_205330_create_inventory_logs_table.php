<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('inventory_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // المستخدم الذي قام بالتغيير
            $table->enum('type', ['purchase', 'sale', 'adjustment', 'transfer', 'return', 'damage', 'expired']);
            $table->integer('quantity_before'); // الكمية قبل التغيير
            $table->integer('quantity_change'); // التغيير في الكمية (موجب أو سالب)
            $table->integer('quantity_after'); // الكمية بعد التغيير
            $table->decimal('unit_cost', 10, 2)->nullable(); // تكلفة الوحدة
            $table->decimal('total_cost', 10, 2)->nullable(); // التكلفة الإجمالية
            $table->string('reference_type')->nullable(); // نوع المرجع (sale, purchase, etc.)
            $table->unsignedBigInteger('reference_id')->nullable(); // معرف المرجع
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();

            // Indexes
            $table->index(['company_id', 'branch_id', 'product_id']);
            $table->index(['type', 'created_at']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_logs');
    }
};
