<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->string('plan_name'); // اسم الخطة
            $table->decimal('base_price', 10, 2); // السعر الأساسي
            $table->integer('included_branches')->default(1); // عدد الفروع المشمولة
            $table->integer('additional_branches')->default(0); // الفروع الإضافية
            $table->decimal('additional_branch_price', 10, 2)->default(0); // سعر الفرع الإضافي
            $table->decimal('total_price', 10, 2); // السعر الإجمالي
            $table->enum('billing_cycle', ['monthly', 'quarterly', 'yearly'])->default('monthly');
            $table->enum('status', ['active', 'cancelled', 'expired', 'suspended'])->default('active');
            $table->date('start_date');
            $table->date('end_date');
            $table->date('next_billing_date');
            $table->boolean('auto_renew')->default(true);
            $table->json('features')->nullable(); // الميزات المشمولة
            $table->timestamps();

            // Indexes
            $table->index(['company_id', 'status']);
            $table->index(['end_date', 'status']);
            $table->index('next_billing_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
