# 📖 دليل الاستخدام - نظام وثاق لنقاط البيع

## 🌐 الوصول للنظام

### الروابط الأساسية
- **واجهة المستخدم (Frontend)**: http://localhost:5173
- **API Backend**: http://localhost:8000/api
- **Laravel Backend**: http://localhost:8000

### 🔐 بيانات الدخول الافتراضية

#### مدير النظام (Super Admin)
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Admin@123456
- **الصلاحيات**: إدارة كاملة للنظام

#### مدير شركة (Company Admin)
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Company@123456
- **الصلاحيات**: إدارة الشركة والفروع

#### كاشير (Cashier)
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: Cashier@123456
- **الصلاحيات**: نقاط البيع والمبيعات

## 🚀 البدء السريع

### 1. تشغيل النظام
```bash
# تشغيل Backend
cd wisaq-pos-backend
php artisan serve

# تشغيل Frontend (في terminal آخر)
cd wisaq-pos-frontend
npm run dev
```

### 2. الوصول للواجهة
1. اذهب إلى http://localhost:5173
2. ستظهر صفحة تسجيل الدخول
3. استخدم بيانات الدخول المذكورة أعلاه
4. انقر على "استخدام البيانات التجريبية" للتعبئة السريعة

## 🏪 استخدام نقطة البيع (POS)

### الوصول لنقطة البيع
1. من القائمة الجانبية، انقر على "نقاط البيع"
2. ستفتح واجهة نقطة البيع الرئيسية

### إضافة منتجات للسلة
1. **البحث بالاسم**: اكتب اسم المنتج في مربع البحث
2. **البحث بالباركود**: امسح الباركود أو اكتبه
3. **التصفح**: تصفح المنتجات من القائمة
4. انقر على المنتج لإضافته للسلة

### إدارة السلة
- **تعديل الكمية**: انقر على + أو - بجانب المنتج
- **حذف منتج**: انقر على أيقونة الحذف
- **مسح السلة**: انقر على "مسح الكل"

### إتمام عملية البيع
1. اختر العميل (اختياري)
2. أضف خصم إن وجد
3. اختر طريقة الدفع (نقدي، بطاقة، تحويل)
4. انقر على "إتمام البيع"
5. اطبع الفاتورة

## 📦 إدارة المنتجات

### إضافة منتج جديد
1. انقر على "المنتجات" من القائمة
2. انقر على "إضافة منتج جديد"
3. املأ البيانات المطلوبة:
   - اسم المنتج (عربي وإنجليزي)
   - الباركود (اختياري)
   - رمز المنتج (SKU)
   - التصنيف
   - سعر الشراء والبيع
   - الكمية الحالية
   - الحد الأدنى للتنبيه
4. انقر على "حفظ"

### تعديل منتج
1. من قائمة المنتجات، انقر على "تعديل"
2. عدل البيانات المطلوبة
3. انقر على "حفظ التغييرات"

### إدارة المخزون
- **عرض المخزون**: انظر الكميات الحالية
- **تنبيهات النفاد**: ستظهر تنبيهات للمنتجات القاربة على النفاد
- **تحديث الكميات**: عدل الكميات عند الحاجة

## 👥 إدارة العملاء

### إضافة عميل جديد
1. انقر على "العملاء" من القائمة
2. انقر على "إضافة عميل جديد"
3. املأ البيانات:
   - الاسم
   - البريد الإلكتروني (اختياري)
   - رقم الهاتف
   - العنوان
   - نوع العميل (فرد/شركة)
4. انقر على "حفظ"

### البحث عن عميل
- استخدم مربع البحث للبحث بالاسم أو الهاتف
- انقر على العميل لعرض تفاصيله

## 📊 التقارير

### تقرير المبيعات
1. انقر على "التقارير" من القائمة
2. اختر "تقرير المبيعات"
3. حدد الفترة الزمنية
4. انقر على "عرض التقرير"

### تقرير المخزون
1. اختر "تقرير المخزون"
2. سيظهر جميع المنتجات مع كمياتها
3. يمكن تصدير التقرير بصيغة PDF أو Excel

### لوحة التحكم
- عرض إحصائيات سريعة
- المبيعات اليومية
- المنتجات الأكثر مبيعاً
- تنبيهات المخزون

## ⚙️ الإعدادات

### إعدادات الشركة
1. انقر على "الإعدادات"
2. عدل بيانات الشركة:
   - اسم الشركة
   - العنوان
   - رقم الهاتف
   - الرقم الضريبي
3. انقر على "حفظ"

### إعدادات النظام
- **العملة الافتراضية**: ريال سعودي
- **معدل الضريبة**: 15%
- **اللغة**: العربية/الإنجليزية

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### لا يمكن الوصول للنظام
1. تأكد من تشغيل Backend: `php artisan serve`
2. تأكد من تشغيل Frontend: `npm run dev`
3. تحقق من الروابط الصحيحة

#### خطأ في تسجيل الدخول
1. تأكد من صحة البريد الإلكتروني وكلمة المرور
2. تحقق من اتصال قاعدة البيانات
3. امسح cache المتصفح

#### مشكلة في إضافة منتج
1. تأكد من ملء جميع الحقول المطلوبة
2. تحقق من عدم تكرار الباركود أو SKU
3. تأكد من صحة الأسعار والكميات

#### مشكلة في الطباعة
1. تأكد من تشغيل الطابعة
2. تحقق من إعدادات المتصفح للطباعة
3. جرب طباعة صفحة اختبار

## 📱 نصائح للاستخدام الأمثل

### لنقطة البيع
- استخدم اختصارات لوحة المفاتيح للسرعة
- احتفظ بقائمة المنتجات الأكثر مبيعاً في المقدمة
- تأكد من تحديث الأسعار بانتظام

### لإدارة المخزون
- راجع تقارير المخزون أسبوعياً
- اضبط الحد الأدنى للتنبيه لكل منتج
- استخدم الباركود لتسريع العمليات

### للتقارير
- راجع التقارير يومياً لمتابعة الأداء
- صدر التقارير الشهرية للمحاسبة
- استخدم التقارير لاتخاذ قرارات الشراء

## 🆘 طلب المساعدة

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 50 123 4567
- **ساعات العمل**: 9 صباحاً - 6 مساءً (السبت - الخميس)

### الموارد الإضافية
- **دليل المطور**: docs/developer-guide.pdf
- **فيديوهات تعليمية**: https://wisaq.com/tutorials
- **الأسئلة الشائعة**: https://wisaq.com/faq

---

**نتمنى لك تجربة ممتعة مع نظام وثاق لنقاط البيع! 🎉**
