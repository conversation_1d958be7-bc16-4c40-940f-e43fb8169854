<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sale_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('product_name'); // اسم المنتج وقت البيع
            $table->string('product_sku'); // رمز المنتج وقت البيع
            $table->integer('quantity');
            $table->decimal('unit_price', 10, 2); // سعر الوحدة وقت البيع
            $table->decimal('discount_amount', 10, 2)->default(0); // خصم البند
            $table->decimal('tax_rate', 5, 2)->default(15.00); // معدل الضريبة
            $table->decimal('tax_amount', 10, 2)->default(0); // قيمة الضريبة
            $table->decimal('total_amount', 10, 2); // المجموع الإجمالي للبند
            $table->timestamps();

            // Indexes
            $table->index(['sale_id', 'product_id']);
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_items');
    }
};
