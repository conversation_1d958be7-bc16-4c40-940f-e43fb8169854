<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // الكاشير
            $table->foreignId('customer_id')->nullable()->constrained()->onDelete('set null');
            $table->string('invoice_number')->unique(); // رقم الفاتورة
            $table->enum('type', ['sale', 'return', 'exchange'])->default('sale');
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded'])->default('completed');
            $table->decimal('subtotal', 10, 2); // المجموع الفرعي
            $table->decimal('discount_amount', 10, 2)->default(0); // قيمة الخصم
            $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة الخصم
            $table->decimal('tax_amount', 10, 2)->default(0); // قيمة الضريبة
            $table->decimal('total_amount', 10, 2); // المجموع الإجمالي
            $table->decimal('paid_amount', 10, 2)->default(0); // المبلغ المدفوع
            $table->decimal('change_amount', 10, 2)->default(0); // الباقي
            $table->enum('payment_method', ['cash', 'card', 'transfer', 'credit', 'mixed'])->default('cash');
            $table->json('payment_details')->nullable(); // تفاصيل الدفع
            $table->text('notes')->nullable(); // ملاحظات
            $table->boolean('is_printed')->default(false); // هل تم طباعة الفاتورة
            $table->timestamp('sale_date'); // تاريخ البيع
            $table->timestamps();

            // Indexes
            $table->index(['company_id', 'branch_id', 'sale_date']);
            $table->index(['invoice_number', 'company_id']);
            $table->index(['user_id', 'sale_date']);
            $table->index(['customer_id', 'sale_date']);
            $table->index(['status', 'type']);
            $table->index('sale_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales');
    }
};
